{"summary": {"total_tests": 67, "passed_tests": 56, "failed_tests": 11, "success_rate": 83.5820895522388, "test_date": "2025-06-16T04:51:27.207859"}, "detailed_results": [{"endpoint": "/cors/preflight", "method": "OPTIONS", "status": "PASS", "expected_code": 200, "actual_code": 204, "message": "", "timestamp": "2025-06-16T04:48:35.246580"}, {"endpoint": "/auth/login", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:48:38.345680"}, {"endpoint": "/auth/register", "method": "POST", "status": "PASS", "expected_code": 201, "actual_code": 201, "message": "", "timestamp": "2025-06-16T04:48:41.585863"}, {"endpoint": "/auth/profile", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:48:43.792229"}, {"endpoint": "/auth/refresh", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:48:46.191762"}, {"endpoint": "/auth/verify-email", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T04:48:48.362376"}, {"endpoint": "/auth/forgot-password", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:48:50.710467"}, {"endpoint": "/auth/reset-password", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T04:48:52.756165"}, {"endpoint": "/auth/logout", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:48:55.119020"}, {"endpoint": "/two-factor/setup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:02.896364"}, {"endpoint": "/two-factor/verify", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:05.118255"}, {"endpoint": "/two-factor/recovery-codes", "method": "GET", "status": "FAIL", "expected_code": 200, "actual_code": 400, "message": "{'error': {'statusCode': 400, 'name': 'BadRequestError', 'message': 'Two-factor authentication is not enabled'}}", "timestamp": "2025-06-16T04:49:07.316776"}, {"endpoint": "/two-factor/regenerate-codes", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:09.616912"}, {"endpoint": "/two-factor/disable", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 422, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:11.814899"}, {"endpoint": "/oauth/providers", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:13.886257"}, {"endpoint": "/oauth/google/url", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:15.927073"}, {"endpoint": "/oauth/github/url", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:17.992914"}, {"endpoint": "/oauth/exchange-token", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:20.396203"}, {"endpoint": "/oauth/callback", "method": "GET", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'error': \"HTTPConnectionPool(host='localhost', port=80): Max retries exceeded with url: /auth/oauth-error?error=Missing+authorization+code+or+state+parameter (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000014E7FB39F90>: Failed to establish a new connection: [WinError 10061] No connection could be made because the target machine actively refused it'))\"}", "timestamp": "2025-06-16T04:49:26.524815"}, {"endpoint": "/payments/test", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:28.562610"}, {"endpoint": "/payments/create-order", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:30.908159"}, {"endpoint": "/payments/verify", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:33.337153"}, {"endpoint": "/payments/user", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:35.782619"}, {"endpoint": "/payments/analytics", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:38.140037"}, {"endpoint": "/payments/refunds", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:40.589926"}, {"endpoint": "/payments/refund", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 404, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:42.994744"}, {"endpoint": "/payments/cancel", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 404, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:45.338906"}, {"endpoint": "/payments/status/{order_id}", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:47.739100"}, {"endpoint": "/payments/webhook", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T04:49:49.793681"}, {"endpoint": "/account/request-deletion", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:52.452396"}, {"endpoint": "/account/deletion-status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:49:54.740296"}, {"endpoint": "/account/cancel-deletion", "method": "POST", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'exception': {'type': 'ThrottleRequestsException', 'namespace': 'masonite.exceptions.exceptions.ThrottleRequestsException'}, 'message': 'Too many attempts', 'stacktrace': [{'index': 4, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\route\\\\ThrottleRequestsMiddleware.py', 'line': 37, 'statement': 'before'}, {'index': 3, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\middleware_capsule.py', 'line': 68, 'statement': 'run_route_middleware'}, {'index': 2, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\providers\\\\RouteProvider.py', 'line': 45, 'statement': 'boot'}, {'index': 1, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\container\\\\container.py', 'line': 259, 'statement': 'resolve'}, {'index': 0, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\foundation\\\\response_handler.py', 'line': 37, 'statement': 'response_handler'}]}", "timestamp": "2025-06-16T04:49:56.929435"}, {"endpoint": "/account/export-data", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:03.509911"}, {"endpoint": "/account/request-export", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:06.014284"}, {"endpoint": "/account/cleanup-expired", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:08.217696"}, {"endpoint": "/account/confirm-deletion", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T04:50:10.431512"}, {"endpoint": "/account/check-preserved-data", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:12.718429"}, {"endpoint": "/account/restore-data", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 400, "message": "Expected validation error", "timestamp": "2025-06-16T04:50:15.058426"}, {"endpoint": "/account/delete-preserved-data", "method": "DELETE", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:17.356922"}, {"endpoint": "/otp/send", "method": "POST", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'exception': {'type': 'ThrottleRequestsException', 'namespace': 'masonite.exceptions.exceptions.ThrottleRequestsException'}, 'message': 'Too many attempts', 'stacktrace': [{'index': 4, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\route\\\\ThrottleRequestsMiddleware.py', 'line': 37, 'statement': 'before'}, {'index': 3, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\middleware_capsule.py', 'line': 68, 'statement': 'run_route_middleware'}, {'index': 2, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\providers\\\\RouteProvider.py', 'line': 45, 'statement': 'boot'}, {'index': 1, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\container\\\\container.py', 'line': 259, 'statement': 'resolve'}, {'index': 0, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\foundation\\\\response_handler.py', 'line': 37, 'statement': 'response_handler'}]}", "timestamp": "2025-06-16T04:50:19.438601"}, {"endpoint": "/otp/send-email", "method": "POST", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'exception': {'type': 'ThrottleRequestsException', 'namespace': 'masonite.exceptions.exceptions.ThrottleRequestsException'}, 'message': 'Too many attempts', 'stacktrace': [{'index': 4, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\route\\\\ThrottleRequestsMiddleware.py', 'line': 37, 'statement': 'before'}, {'index': 3, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\middleware_capsule.py', 'line': 68, 'statement': 'run_route_middleware'}, {'index': 2, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\providers\\\\RouteProvider.py', 'line': 45, 'statement': 'boot'}, {'index': 1, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\container\\\\container.py', 'line': 259, 'statement': 'resolve'}, {'index': 0, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\foundation\\\\response_handler.py', 'line': 37, 'statement': 'response_handler'}]}", "timestamp": "2025-06-16T04:50:21.493871"}, {"endpoint": "/otp/send-sms", "method": "POST", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'error': {'statusCode': 500, 'name': 'InternalServerError', 'message': 'Phone number not registered. Please register first or use email.'}}", "timestamp": "2025-06-16T04:50:23.766390"}, {"endpoint": "/otp/verify", "method": "POST", "status": "FAIL", "expected_code": 400, "actual_code": 200, "message": "{'valid': False, 'message': 'No valid OTP found or OTP expired'}", "timestamp": "2025-06-16T04:50:26.064091"}, {"endpoint": "/otp/login", "method": "POST", "status": "FAIL", "expected_code": 400, "actual_code": 500, "message": "{'exception': {'type': 'ThrottleRequestsException', 'namespace': 'masonite.exceptions.exceptions.ThrottleRequestsException'}, 'message': 'Too many attempts', 'stacktrace': [{'index': 4, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\route\\\\ThrottleRequestsMiddleware.py', 'line': 37, 'statement': 'before'}, {'index': 3, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\middleware\\\\middleware_capsule.py', 'line': 68, 'statement': 'run_route_middleware'}, {'index': 2, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\providers\\\\RouteProvider.py', 'line': 45, 'statement': 'boot'}, {'index': 1, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\container\\\\container.py', 'line': 259, 'statement': 'resolve'}, {'index': 0, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\foundation\\\\response_handler.py', 'line': 37, 'statement': 'response_handler'}]}", "timestamp": "2025-06-16T04:50:28.135260"}, {"endpoint": "/otp/status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:30.651950"}, {"endpoint": "/otp/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:36.252373"}, {"endpoint": "/security/dashboard", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:39.105296"}, {"endpoint": "/security/events", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:41.509479"}, {"endpoint": "/security/events/user", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:43.830223"}, {"endpoint": "/security/suspicious-activity", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:46.208040"}, {"endpoint": "/security/analysis", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:48.536626"}, {"endpoint": "/security/statistics", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:51.418971"}, {"endpoint": "/security/account-status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:53.936935"}, {"endpoint": "/security/unlock-account", "method": "POST", "status": "PASS", "expected_code": 400, "actual_code": 404, "message": "Expected validation error", "timestamp": "2025-06-16T04:50:56.245481"}, {"endpoint": "/security/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:50:58.603775"}, {"endpoint": "/security/events/{event_id}/resolve", "method": "POST", "status": "FAIL", "expected_code": 400, "actual_code": 500, "message": "{'exception': {'type': 'RouteNotFoundException', 'namespace': 'masonite.exceptions.exceptions.RouteNotFoundException'}, 'message': 'POST /api/security/events/invalid_id/resolve : 404 Not Found', 'stacktrace': [{'index': 2, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\providers\\\\RouteProvider.py', 'line': 30, 'statement': 'boot'}, {'index': 1, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\container\\\\container.py', 'line': 259, 'statement': 'resolve'}, {'index': 0, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\foundation\\\\response_handler.py', 'line': 37, 'statement': 'response_handler'}]}", "timestamp": "2025-06-16T04:51:00.661777"}, {"endpoint": "/notifications", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:02.957718"}, {"endpoint": "/notifications/test", "method": "POST", "status": "FAIL", "expected_code": 200, "actual_code": 500, "message": "{'error': {'statusCode': 500, 'name': 'InternalServerError', 'message': 'Failed to send test notification'}}", "timestamp": "2025-06-16T04:51:05.134766"}, {"endpoint": "/notifications/{notification_id}/read", "method": "POST", "status": "FAIL", "expected_code": 400, "actual_code": 500, "message": "{'exception': {'type': 'RouteNotFoundException', 'namespace': 'masonite.exceptions.exceptions.RouteNotFoundException'}, 'message': 'POST /api/notifications/invalid_id/read : 404 Not Found', 'stacktrace': [{'index': 2, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\providers\\\\RouteProvider.py', 'line': 30, 'statement': 'boot'}, {'index': 1, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\container\\\\container.py', 'line': 259, 'statement': 'resolve'}, {'index': 0, 'file': 'C:\\\\Users\\\\<USER>\\\\anaconda3\\\\envs\\\\masonite-secure-env\\\\Lib\\\\site-packages\\\\masonite\\\\foundation\\\\response_handler.py', 'line': 37, 'statement': 'response_handler'}]}", "timestamp": "2025-06-16T04:51:07.193951"}, {"endpoint": "/queue/status", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:09.699931"}, {"endpoint": "/queue/stats", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:11.978130"}, {"endpoint": "/queue/failed-jobs", "method": "GET", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:14.271816"}, {"endpoint": "/queue/test-email", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:16.607582"}, {"endpoint": "/queue/test-security-processing", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:19.043532"}, {"endpoint": "/queue/test-cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:21.584038"}, {"endpoint": "/queue/cleanup", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:24.793565"}, {"endpoint": "/queue/process-security-events", "method": "POST", "status": "PASS", "expected_code": 200, "actual_code": 200, "message": "", "timestamp": "2025-06-16T04:51:27.204858"}]}